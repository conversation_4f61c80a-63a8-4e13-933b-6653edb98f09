import React from 'react';
import { createRoot } from 'react-dom/client';
import { SessionContextProvider } from '@benzinga/session-context';
import { createSession } from '@benzinga/session';
import { environmentVariables } from '../env';
import { useAuth } from './hooks/useAuth';
import { Auth } from './components/Auth';
import Squawk from './components/Squawk';

const App = () => {
  const session = createSession(environmentVariables.SESSION_ENV);
  return (
    <SessionContextProvider session={session}>
      <Main />
    </SessionContextProvider>
  );
};

const auth = {
  key: '1sgve3qtz6ssghndnfm7vg5gpkib3c5n',
  user: {
    email: '<EMAIL>',
  },
};

const isLoggedIn = true;

const Main = () => {
  //const { auth, isLoggedIn } = useAuth();
  if (!isLoggedIn || !auth) return <Auth />;
  console.log('auth3', auth);
  return <Squawk token={auth.key} username={auth.user.email} />;
};

const element = document.getElementById('root');
const root = createRoot(element as HTMLElement);
root.render(<App />);
