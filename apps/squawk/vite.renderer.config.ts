import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import { nxViteTsPaths } from '@nx/vite/plugins/nx-tsconfig-paths.plugin';
import { environmentVariables } from './env';
import svgr from 'vite-plugin-svgr';

// https://vitejs.dev/config
export default defineConfig({
  build: {
    commonjsOptions: {
      transformMixedEsModules: true,
    },
    rollupOptions: {
      external: ['@juggle/resize-observer'],
    },
  },
  define: {
    global: 'window',
    'process.env': {
      ...process.env,
      SQUAWK_ADDR: environmentVariables.SESSION_ENV['benzinga-squawk'].url,
    },
  },
  plugins: [
    react(),
    svgr({
      svgrOptions: {
        // svgr options
      },
    }),
    nxViteTsPaths(),
  ],
  server: {
    host: 'local.benzinga.com',
    port: 3000,
    //host: 'localhost',
    //port: 4200,
  },
});
